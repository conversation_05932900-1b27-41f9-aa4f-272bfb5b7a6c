using System.Drawing.Drawing2D;

namespace WinFormsApp1
{
    /// <summary>
    /// 增强版彩票表格绘制帮助类 - 专业级彩票表格生成器
    /// <para>支持两种表格格式：</para>
    /// <para>1. 传统5列详细表格 - DrawPerfectLotteryTable</para>
    /// <para>2. 可配置行数的番摊网格表格 - DrawTanImage7Row</para>
    /// </summary>
    public static class EnhancedLotteryTableDrawer
    {
        #region 常量定义

        // 布局尺寸常量
        /// <summary>传统表格数据行高度</summary>
        private const int RowHeight = 32;

        /// <summary>传统表格标题行高度</summary>
        private const int HeaderHeight = 36;

        /// <summary>Banner广告位宽度</summary>
        private const int BannerWidth = 370;

        /// <summary>Banner广告位高度</summary>
        private const int BannerHeight = 70;

        // 表格结构常量
        /// <summary>传统表格列宽配置：期号、超级号码、番摊、达晓、單雙</summary>
        private static readonly int[] ColumnWidths = { 108, 81, 81, 50, 50 };

        /// <summary>传统表格列标题</summary>
        private static readonly string[] Headers = { @"期号", @"超级号码", @"番摊", @"达晓", @"單雙" };

        // 颜色常量
        /// <summary>表格标题背景色（深青色）</summary>
        private static readonly Color HeaderBackgroundColor = Color.FromArgb(0, 128, 128);

        /// <summary>天蓝色背景色</summary>
        private static readonly Color SkyBlueBackgroundColor = Color.FromArgb(70, 130, 180);

        /// <summary>番摊数字对应的填充颜色数组</summary>
        private static readonly Color[] FanTanColors =
        {
            Color.White, // 默认 (索引0，不使用)
            Color.FromArgb(135, 206, 235), // 天蓝色 (数字1)
            Color.FromArgb(50, 205, 50), // 绿色 (数字2)
            Color.FromArgb(255, 215, 0), // 黄色 (数字3)
            Color.FromArgb(255, 69, 0) // 红色 (数字4)
        };

        #endregion

        #region 公共接口方法

        /// <summary>
        /// 绘制以假乱真级别的彩票开奖结果表格
        /// </summary>
        /// <param name="lotteryResults">彩票开奖结果数据</param>
        /// <param name="topBannerPath">顶部banner图片路径（可选）</param>
        /// <param name="bottomBannerPath">底部banner图片路径（可选）</param>
        /// <returns>绘制好的位图</returns>
        /// <exception cref="ArgumentNullException">当lotteryResults为null时抛出</exception>
        /// <exception cref="ArgumentException">当lotteryResults为空集合时抛出</exception>
        public static Bitmap DrawPerfectLotteryTable(List<LotteryResult> lotteryResults, string? topBannerPath = null, string? bottomBannerPath = null)
        {
            // 将单个数据集转换为多数据集格式，调用多数据集方法
            var lotteryResultsList = new List<List<LotteryResult>> { lotteryResults };
            return DrawMultiplePerfectLotteryTables(lotteryResultsList, topBannerPath, bottomBannerPath);
        }

        /// <summary>
        /// 绘制多个以假乱真级别的彩票开奖结果表格 - 垂直堆叠多个数据集的5列详细表格
        /// </summary>
        /// <param name="lotteryResultsList">多个彩票开奖结果数据集合</param>
        /// <param name="topBannerPath">顶部banner图片路径（可选）</param>
        /// <param name="bottomBannerPath">底部banner图片路径（可选）</param>
        /// <returns>绘制好的位图</returns>
        /// <exception cref="ArgumentNullException">当参数为null时抛出</exception>
        /// <exception cref="ArgumentException">当参数无效时抛出</exception>
        public static Bitmap DrawMultiplePerfectLotteryTables(List<List<LotteryResult>> lotteryResultsList, string? topBannerPath = null, string? bottomBannerPath = null)
        {
            // 参数验证
            if (lotteryResultsList == null)
                throw new ArgumentNullException(nameof(lotteryResultsList));
            if (!lotteryResultsList.Any())
                throw new ArgumentException(@"彩票结果数据集合不能为空", nameof(lotteryResultsList));

            // 验证每个数据集
            for (int i = 0; i < lotteryResultsList.Count; i++)
            {
                if (lotteryResultsList[i] == null)
                    throw new ArgumentException($@"第{i + 1}个彩票结果数据不能为null", nameof(lotteryResultsList));
                if (!lotteryResultsList[i].Any())
                    throw new ArgumentException($@"第{i + 1}个彩票结果数据不能为空", nameof(lotteryResultsList));
            }

            // 计算精确的表格尺寸
            int tableWidth = ColumnWidths.Sum();

            // 计算所有表格的总高度
            int totalTableHeight = 0;
            foreach (var lotteryResults in lotteryResultsList)
            {
                int currentTableHeight = HeaderHeight + (lotteryResults.Count * RowHeight);
                totalTableHeight += currentTableHeight;
            }

            // 计算总图片尺寸（包含banner）
            int totalHeight = totalTableHeight;
            if (!string.IsNullOrEmpty(topBannerPath)) totalHeight += BannerHeight;
            if (!string.IsNullOrEmpty(bottomBannerPath)) totalHeight += BannerHeight;

            int imageWidth = Math.Max(tableWidth, BannerWidth);
            int imageHeight = totalHeight;

            // 创建高质量位图
            Bitmap bitmap = new Bitmap(imageWidth, imageHeight);
            using Graphics graphics = Graphics.FromImage(bitmap);
            // 设置最高质量渲染参数
            SetHighQualityRenderingHints(graphics);

            // 填充白色背景
            graphics.Clear(Color.White);

            // 计算表格绘制起始位置
            int currentY = 0;
            if (!string.IsNullOrEmpty(topBannerPath)) currentY += BannerHeight;

            // 绘制顶部banner
            if (!string.IsNullOrEmpty(topBannerPath))
            {
                DrawBanner(graphics, topBannerPath, 0, 0, imageWidth, BannerHeight);
            }

            // 循环绘制所有精确表格
            foreach (var lotteryResults in lotteryResultsList)
            {
                // 绘制当前表格
                DrawPerfectTable(graphics, lotteryResults, currentY);

                // 更新下一个表格的Y坐标
                int currentTableHeight = HeaderHeight + (lotteryResults.Count * RowHeight);
                currentY += currentTableHeight;
            }

            // 绘制底部banner
            if (!string.IsNullOrEmpty(bottomBannerPath))
            {
                DrawBanner(graphics, bottomBannerPath, 0, currentY, imageWidth, BannerHeight);
            }

            return bitmap;
        }

        /// <summary>
        /// 绘制番摊结果表格 - 可配置行数的网格格式
        /// </summary>
        /// <param name="lotteryResults">彩票开奖结果数据</param>
        /// <param name="gameName">游戏名称（如"宾果3"）</param>
        /// <param name="dataRows">数据行数（默认7行，可选6行、8行等）</param>
        /// <param name="topBannerPath">顶部banner图片路径（可选）</param>
        /// <param name="bottomBannerPath">底部banner图片路径（可选）</param>
        /// <returns>绘制好的位图</returns>
        public static Bitmap DrawTanImage(List<LotteryResult> lotteryResults, string gameName, int dataRows = 7, string? topBannerPath = null, string? bottomBannerPath = null)
        {
            // 将单个数据集转换为多数据集格式，调用多数据集方法
            var lotteryResultsList = new List<List<LotteryResult>> { lotteryResults };
            var gameNames = new List<string> { gameName };
            return DrawMultipleTanImages(lotteryResultsList, gameNames, dataRows, topBannerPath, bottomBannerPath);
        }

        /// <summary>
        /// 绘制多个番摊结果表格 - 垂直堆叠多个游戏的网格表格
        /// </summary>
        /// <param name="lotteryResultsList">多个彩票开奖结果数据集合</param>
        /// <param name="gameNames">对应的游戏名称集合</param>
        /// <param name="dataRows">每个表格的数据行数（默认7行）</param>
        /// <param name="topBannerPath">顶部banner图片路径（可选）</param>
        /// <param name="bottomBannerPath">底部banner图片路径（可选）</param>
        /// <returns>绘制好的位图</returns>
        /// <exception cref="ArgumentNullException">当参数为null时抛出</exception>
        /// <exception cref="ArgumentException">当参数无效时抛出</exception>
        public static Bitmap DrawMultipleTanImages(List<List<LotteryResult>> lotteryResultsList, List<string> gameNames, int dataRows = 7, string? topBannerPath = null, string? bottomBannerPath = null)
        {
            // 参数验证
            if (lotteryResultsList == null)
                throw new ArgumentNullException(nameof(lotteryResultsList));
            if (gameNames == null)
                throw new ArgumentNullException(nameof(gameNames));
            if (!lotteryResultsList.Any())
                throw new ArgumentException(@"彩票结果数据集合不能为空", nameof(lotteryResultsList));
            if (!gameNames.Any())
                throw new ArgumentException(@"游戏名称集合不能为空", nameof(gameNames));
            if (lotteryResultsList.Count != gameNames.Count)
                throw new ArgumentException(@"彩票结果数据集合与游戏名称集合的数量必须相等");
            if (dataRows < 1 || dataRows > 20)
                throw new ArgumentOutOfRangeException(nameof(dataRows), @"数据行数必须在1-20之间");

            // 验证每个数据集
            for (int i = 0; i < lotteryResultsList.Count; i++)
            {
                if (lotteryResultsList[i] == null)
                    throw new ArgumentException($@"第{i + 1}个彩票结果数据不能为null", nameof(lotteryResultsList));
                if (!lotteryResultsList[i].Any())
                    throw new ArgumentException($@"第{i + 1}个彩票结果数据不能为空", nameof(lotteryResultsList));
                if (string.IsNullOrWhiteSpace(gameNames[i]))
                    throw new ArgumentException($@"第{i + 1}个游戏名称不能为空", nameof(gameNames));
            }

            // 番摊表格参数 - 调整为370px宽度
            const int GridCols = 11;
            const int HeaderRowHeight = 35; // 调高标题行高度，提供更好的视觉效果
            const int DataRowHeight = 33; // 数据行高度与列宽一致

            // 计算表格尺寸 - 确保宽度精确为370px
            int tableWidth = 370; // 固定宽度370px

            // 计算所有表格的总高度
            int totalTableHeight = 0;
            for (int i = 0; i < lotteryResultsList.Count; i++)
            {
                // 每个表格的实际数据行数（不超过指定的dataRows）
                int actualDataRows = Math.Min(dataRows, lotteryResultsList[i].Count);
                int currentTableHeight = HeaderRowHeight + actualDataRows * DataRowHeight;
                totalTableHeight += currentTableHeight;
            }

            // 计算总图片尺寸（包含banner）
            int totalHeight = totalTableHeight;
            if (!string.IsNullOrEmpty(topBannerPath)) totalHeight += BannerHeight;
            if (!string.IsNullOrEmpty(bottomBannerPath)) totalHeight += BannerHeight;

            int imageWidth = Math.Max(tableWidth, BannerWidth);
            int imageHeight = totalHeight;

            // 创建高质量位图
            Bitmap bitmap = new Bitmap(imageWidth, imageHeight);
            using Graphics graphics = Graphics.FromImage(bitmap);
            // 设置高质量渲染
            SetHighQualityRenderingHints(graphics);

            // 填充白色背景
            graphics.Clear(Color.White);

            // 计算表格绘制起始位置
            int currentY = 0;
            if (!string.IsNullOrEmpty(topBannerPath)) currentY += BannerHeight;

            // 绘制顶部banner
            if (!string.IsNullOrEmpty(topBannerPath))
            {
                DrawBanner(graphics, topBannerPath, 0, 0, imageWidth, BannerHeight);
            }

            // 循环绘制所有番摊表格
            for (int i = 0; i < lotteryResultsList.Count; i++)
            {
                var currentLotteryResults = lotteryResultsList[i];
                var currentGameName = gameNames[i];

                // 计算当前表格的实际行数
                int actualDataRows = Math.Min(dataRows, currentLotteryResults.Count);
                int totalRows = actualDataRows + 1; // 数据行 + 标题行

                // 绘制当前番摊表格
                DrawTanGrid(graphics, currentLotteryResults, currentGameName, currentY, tableWidth, totalRows, GridCols, DataRowHeight, HeaderRowHeight);

                // 更新下一个表格的Y坐标
                int currentTableHeight = HeaderRowHeight + actualDataRows * DataRowHeight;
                currentY += currentTableHeight;
            }

            // 绘制底部banner
            if (!string.IsNullOrEmpty(bottomBannerPath))
            {
                DrawBanner(graphics, bottomBannerPath, 0, currentY, imageWidth, BannerHeight);
            }

            return bitmap;
        }

        #endregion

        #region Banner绘制方法

        /// <summary>
        /// 设置高质量渲染参数
        /// </summary>
        private static void SetHighQualityRenderingHints(Graphics graphics)
        {
            graphics.SmoothingMode = SmoothingMode.HighQuality;
            graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
            graphics.CompositingQuality = CompositingQuality.HighQuality;
            graphics.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;
            graphics.PixelOffsetMode = PixelOffsetMode.HighQuality;
        }

        /// <summary>
        /// 绘制Banner广告
        /// </summary>
        private static void DrawBanner(Graphics graphics, string? bannerPath, int x, int y, int width, int height)
        {
            try
            {
                if (File.Exists(bannerPath))
                {
                    using Image bannerImage = Image.FromFile(bannerPath);
                    // 绘制banner图片，自动缩放到指定尺寸
                    graphics.DrawImage(bannerImage, x, y, width, height);
                }
                else
                {
                    // 如果图片不存在，绘制占位符
                    DrawBannerPlaceholder(graphics, x, y, width, height, Path.GetFileName(bannerPath));
                }
            }
            catch
            {
                // 如果加载图片失败，绘制错误占位符
                DrawBannerPlaceholder(graphics, x, y, width, height, @"图片加载失败");
            }
        }

        /// <summary>
        /// 绘制Banner占位符
        /// </summary>
        private static void DrawBannerPlaceholder(Graphics graphics, int x, int y, int width, int height, string? text)
        {
            Rectangle bannerRect = new Rectangle(x, y, width, height);

            // 绘制浅灰色背景
            using (SolidBrush backgroundBrush = new SolidBrush(Color.FromArgb(240, 240, 240)))
            {
                graphics.FillRectangle(backgroundBrush, bannerRect);
            }

            // 绘制边框
            using (Pen borderPen = new Pen(Color.FromArgb(200, 200, 200), 1))
            {
                graphics.DrawRectangle(borderPen, bannerRect);
            }

            // 绘制占位文字
            using (Font placeholderFont = new Font(@"宋体", 12, FontStyle.Regular))
            using (SolidBrush textBrush = new SolidBrush(Color.FromArgb(150, 150, 150)))
            {
                StringFormat stringFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };
                graphics.DrawString($@"Banner广告位 ({text})", placeholderFont, textBrush, bannerRect, stringFormat);
            }
        }

        #endregion

        #region 番摊表格绘制方法

        /// <summary>
        /// 绘制番摊网格表格
        /// </summary>
        private static void DrawTanGrid(Graphics graphics, List<LotteryResult> lotteryResults, string gameName, int startY, int tableWidth, int totalRows, int gridCols, int cellSize, int headerRowHeight)
        {
            // 表格宽度固定为370px，居中显示
            int startX = (tableWidth > BannerWidth) ? 0 : (BannerWidth - tableWidth) / 2;

            // 绘制第1行标题（合并单元格）
            DrawTanGridHeader(graphics, lotteryResults, gameName, startX, startY, tableWidth, headerRowHeight);

            // 绘制数据网格 - 传入实际数据行数
            int dataRows = totalRows - 1; // 总行数 - 标题行 = 数据行数
            DrawTanGridData(graphics, lotteryResults, startX, startY + headerRowHeight, dataRows, gridCols, tableWidth);
        }

        /// <summary>
        /// 绘制番摊表格标题行
        /// </summary>
        private static void DrawTanGridHeader(Graphics graphics, List<LotteryResult> lotteryResults, string gameName, int x, int y, int width, int height)
        {
            Rectangle headerRect = new Rectangle(x, y, width, height);

            // 绘制标题背景（与原表格标题相同的深青色）
            using (SolidBrush headerBrush = new SolidBrush(HeaderBackgroundColor))
            {
                graphics.FillRectangle(headerBrush, headerRect);
            }

            // 绘制标题边框
            using (Pen borderPen = new Pen(Color.Black, 0.5f))
            {
                graphics.DrawRectangle(borderPen, headerRect);
            }

            // 构建标题文字：[ 起始期号 - 结束期号 ] 游戏名称
            string titleText = BuildTitleText(lotteryResults, gameName);

            // 绘制标题文字
            using (Font headerFont = new Font(@"宋体", 12, FontStyle.Bold)) // 稍微调小字体以适应更长的文字
            using (SolidBrush textBrush = new SolidBrush(Color.White))
            {
                StringFormat stringFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };
                graphics.DrawString(titleText, headerFont, textBrush, headerRect, stringFormat);
            }
        }

        /// <summary>
        /// 构建标题文字
        /// </summary>
        private static string BuildTitleText(List<LotteryResult> lotteryResults, string gameName)
        {
            if (lotteryResults == null || !lotteryResults.Any())
            {
                return $@"[ 无数据 ] {gameName}";
            }

            // 获取起始和结束期号
            string startPeriod = lotteryResults.First().PeriodNumber;
            string endPeriod = lotteryResults.Last().PeriodNumber;

            // 如果只有一个期号
            if (lotteryResults.Count == 1)
            {
                return $@"[ {startPeriod} ] {gameName}";
            }

            // 多个期号显示范围
            return $@"[ {startPeriod} - {endPeriod} ] {gameName}";
        }

        /// <summary>
        /// 绘制番摊数据网格 - 按列填充（先从上到下填满第1列，再填第2列）
        /// </summary>
        private static void DrawTanGridData(Graphics graphics, List<LotteryResult> lotteryResults, int startX, int startY, int dataRows, int cols, int tableWidth)
        {
            // 提取番摊结果
            var fanTanResults = lotteryResults.Select(r => r.FanTan).ToList();

            // 计算实际的列宽和行高 - 确保11列完全填满370px
            int baseColWidth = tableWidth / cols; // 370 ÷ 11 = 33.636... → 33px
            int remainder = tableWidth % cols; // 370 % 11 = 7px余数
            int rowHeight = 33; // 与列宽保持一致，形成正方形单元格

            using (Font fanTanFont = new Font(@"微软雅黑", 14, FontStyle.Bold)) // 与原表格完全一致的14px字体
            using (Pen borderPen = new Pen(Color.Black, 0.5f))
            {
                int resultIndex = 0;

                // 按列填充：先填第1列，再填第2列...
                for (int col = 0; col < cols; col++)
                {
                    // 计算当前列的宽度 - 前几列分配余数像素
                    int currentColWidth = baseColWidth + (col < remainder ? 1 : 0);

                    // 计算当前列的X位置
                    int currentColX = startX;
                    for (int i = 0; i < col; i++)
                    {
                        currentColX += baseColWidth + (i < remainder ? 1 : 0);
                    }

                    for (int row = 0; row < dataRows; row++)
                    {
                        int cellX = currentColX;
                        int cellY = startY + row * rowHeight;
                        Rectangle cellRect = new Rectangle(cellX, cellY, currentColWidth, rowHeight);

                        // 只绘制左边框和上边框，避免重叠
                        // 绘制左边框
                        graphics.DrawLine(borderPen, cellRect.Left, cellRect.Top, cellRect.Left, cellRect.Bottom);
                        // 绘制上边框
                        graphics.DrawLine(borderPen, cellRect.Left, cellRect.Top, cellRect.Right, cellRect.Top);

                        // 完全取消内容绘制，只保留边框排查问题
                        if (resultIndex < fanTanResults.Count)
                        {
                            resultIndex++;
                        }
                        // 不绘制任何内容，只保留边框
                    }
                }

                // 补齐最右列的右边框
                int lastColX = startX;
                for (int i = 0; i < cols; i++)
                {
                    lastColX += baseColWidth + (i < remainder ? 1 : 0);
                }
                for (int row = 0; row < dataRows; row++)
                {
                    int cellY = startY + row * rowHeight;
                    graphics.DrawLine(borderPen, lastColX, cellY, lastColX, cellY + rowHeight);
                }

                // 补齐最下行的下边框
                int lastRowY = startY + dataRows * rowHeight;
                int currentX = startX;
                for (int col = 0; col < cols; col++)
                {
                    int currentColWidth = baseColWidth + (col < remainder ? 1 : 0);
                    graphics.DrawLine(borderPen, currentX, lastRowY, currentX + currentColWidth, lastRowY);
                    currentX += currentColWidth;
                }
            }
        }

        /// <summary>
        /// 绘制单个番摊单元格
        /// </summary>
        private static void DrawTanGridCell(Graphics graphics, Rectangle cellRect, int fanTanValue, Font font)
        {
            // 获取番摊颜色
            Color fillColor = GetFanTanColor(fanTanValue.ToString());

            // 计算圆角矩形（调整为26×26px）
            int margin = (cellRect.Width - 26) / 2; // 居中计算边距
            Rectangle innerRect = new Rectangle(
                cellRect.X + margin,
                cellRect.Y + margin,
                26,
                26);

            // 取消绘制彩色圆角背景，进一步排查边框问题
            // using (SolidBrush fillBrush = new SolidBrush(fillColor))
            // {
            //     FillRoundedRectangle(graphics, fillBrush, innerRect, 4);
            // }

            // 取消绘制圆角边框，避免与单元格边框冲突
            // using (Pen borderPen = new Pen(Color.Black, 0.5f))
            // {
            //     DrawRoundedRectangle(graphics, borderPen, innerRect, 4);
            // }

            // 暂时取消轮廓文字，排查边框问题
            // using (SolidBrush whiteBrush = new SolidBrush(Color.White))
            // {
            //     DrawOutlinedText(graphics, fanTanValue.ToString(), font, whiteBrush, cellRect);
            // }

            // 使用普通文字绘制
            using (SolidBrush blackBrush = new SolidBrush(Color.Black))
            {
                StringFormat stringFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center,
                    FormatFlags = StringFormatFlags.NoWrap
                };
                graphics.DrawString(fanTanValue.ToString(), font, blackBrush, cellRect, stringFormat);
            }
        }

        #endregion

        #region 表格绘制核心方法

        /// <summary>
        /// 绘制精确表格
        /// </summary>
        private static void DrawPerfectTable(Graphics graphics, List<LotteryResult> lotteryResults, int startY = 0)
        {
            // 字体定义
            using Font headerFont = new Font(@"宋体", 12, FontStyle.Bold);
            using Font dataFont = new Font(@"宋体", 11, FontStyle.Bold);
            using Font fanTanFont = new Font(@"微软雅黑", 14, FontStyle.Bold);
            using Font superNumberFont = new Font(@"宋体", 12, FontStyle.Bold);
            using Pen borderPen = new Pen(Color.Black, 0.5f);
            using SolidBrush whiteBrush = new SolidBrush(Color.White);
            using SolidBrush blackBrush = new SolidBrush(Color.Black);
            using SolidBrush redBrush = new SolidBrush(Color.Red);
            using SolidBrush blueBrush = new SolidBrush(Color.Blue);
            int currentY = startY;

            // 绘制精确表头
            DrawPerfectHeader(graphics, headerFont, whiteBrush, borderPen, ref currentY);

            // 绘制精确数据行
            foreach (var lotteryResult in lotteryResults)
            {
                DrawPerfectDataRow(graphics, lotteryResult, dataFont, fanTanFont, superNumberFont, whiteBrush, blackBrush, redBrush, blueBrush, borderPen, ref currentY);
            }
        }

        /// <summary>
        /// 绘制精确表头 - 隐藏指定竖线
        /// </summary>
        private static void DrawPerfectHeader(Graphics graphics, Font font, SolidBrush textBrush, Pen borderPen, ref int currentY)
        {
            int currentX = 0;

            for (int col = 0; col < Headers.Length; col++)
            {
                Rectangle cellRect = new Rectangle(currentX, currentY, ColumnWidths[col], HeaderHeight);

                // 绘制表头背景（带渐变效果）
                using (LinearGradientBrush gradientBrush = new LinearGradientBrush(
                           cellRect,
                           Color.FromArgb(10, 138, 138),
                           HeaderBackgroundColor,
                           LinearGradientMode.Vertical))
                {
                    graphics.FillRectangle(gradientBrush, cellRect);
                }

                // 绘制表头边框 - 隐藏第2,3,4,5列之间的竖线
                DrawSelectiveHeaderBorder(graphics, borderPen, cellRect, col);

                // 绘制文字（带阴影效果）
                DrawPerfectText(graphics, Headers[col], font, textBrush, cellRect, true);

                currentX += ColumnWidths[col];
            }

            currentY += HeaderHeight;
        }

        /// <summary>
        /// 选择性绘制表头边框 - 隐藏指定的竖线
        /// </summary>
        private static void DrawSelectiveHeaderBorder(Graphics graphics, Pen borderPen, Rectangle cellRect, int columnIndex)
        {
            // 绘制上边框
            graphics.DrawLine(borderPen, cellRect.Left, cellRect.Top, cellRect.Right, cellRect.Top);

            // 绘制下边框
            graphics.DrawLine(borderPen, cellRect.Left, cellRect.Bottom, cellRect.Right, cellRect.Bottom);

            // 绘制左边框（只有第一列）
            if (columnIndex == 0)
            {
                graphics.DrawLine(borderPen, cellRect.Left, cellRect.Top, cellRect.Left, cellRect.Bottom);
            }

            // 绘制右边框（只有第一列和最后一列）
            if (columnIndex == 0 || columnIndex == 4)
            {
                graphics.DrawLine(borderPen, cellRect.Right, cellRect.Top, cellRect.Right, cellRect.Bottom);
            }
        }

        /// <summary>
        /// 绘制精确数据行 - 隐藏指定竖线，彩色字体
        /// </summary>
        private static void DrawPerfectDataRow(Graphics graphics, LotteryResult result, Font font, Font fanTanFont, Font superNumberFont, SolidBrush whiteBrush, SolidBrush blackBrush, SolidBrush redBrush, SolidBrush blueBrush, Pen borderPen, ref int currentY)
        {
            int currentX = 0;
            // 构建行数据
            string[] rowData = { result.PeriodNumber, result.Number.ToString(), result.FanTan.ToString(), result.BigSmall, result.SingleDouble };

            for (int col = 0; col < rowData.Length; col++)
            {
                Rectangle cellRect = new Rectangle(currentX, currentY, ColumnWidths[col], RowHeight);
                Color backgroundColor = GetCellBackgroundColor(col);

                // 根据列和内容选择字体颜色
                SolidBrush textBrush = GetTextBrush(col, rowData[col], blackBrush, redBrush, blueBrush, whiteBrush);

                // 绘制白色背景
                graphics.FillRectangle(Brushes.White, cellRect);

                // 如果是超级号码列，绘制特殊的背景
                if (col == 1) // 超级号码列
                {
                    DrawNumberBackground(graphics, rowData[col], superNumberFont, cellRect, backgroundColor);
                }
                // 如果是番摊列，绘制彩色圆角边框
                else if (col == 2) // 番摊列
                {
                    DrawColoredNumberBorder(graphics, rowData[col], fanTanFont, cellRect);
                }

                // 绘制边框 - 隐藏第2,3,4,5列之间的竖线
                DrawSelectiveBorder(graphics, borderPen, cellRect, col);

                // 绘制文字 - 不同列使用专用字体和效果
                Font currentFont = col switch
                {
                    1 => superNumberFont, // 超级号码列使用大字体
                    2 => fanTanFont, // 番摊列使用微软雅黑
                    _ => font // 其他列使用普通字体
                };

                if (col == 2) // 番摊列使用轮廓文字
                {
                    DrawOutlinedText(graphics, rowData[col], currentFont, textBrush, cellRect);
                }
                else
                {
                    DrawPerfectText(graphics, rowData[col], currentFont, textBrush, cellRect, backgroundColor != Color.White);
                }

                currentX += ColumnWidths[col];
            }

            currentY += RowHeight;
        }

        #endregion

        #region 辅助绘制方法

        /// <summary>
        /// 根据列和内容获取文字画刷颜色
        /// </summary>
        private static SolidBrush GetTextBrush(int columnIndex, string text, SolidBrush blackBrush, SolidBrush redBrush, SolidBrush blueBrush, SolidBrush whiteBrush)
        {
            return columnIndex switch
            {
                1 or 2 => whiteBrush, // 超级号码列和番摊列：白色字体
                3 => text == @"小" ? blueBrush : redBrush, // 达晓列：小=蓝色，大=红色
                4 => text == @"單" ? blueBrush : redBrush, // 單雙列：單=蓝色，雙=红色
                _ => blackBrush // 其他列：黑色字体
            };
        }

        /// <summary>
        /// 选择性绘制边框 - 隐藏指定的竖线
        /// </summary>
        private static void DrawSelectiveBorder(Graphics graphics, Pen borderPen, Rectangle cellRect, int columnIndex)
        {
            // 绘制上边框
            graphics.DrawLine(borderPen, cellRect.Left, cellRect.Top, cellRect.Right, cellRect.Top);

            // 绘制下边框
            graphics.DrawLine(borderPen, cellRect.Left, cellRect.Bottom, cellRect.Right, cellRect.Bottom);

            // 绘制左边框（只有第一列）
            if (columnIndex == 0)
            {
                graphics.DrawLine(borderPen, cellRect.Left, cellRect.Top, cellRect.Left, cellRect.Bottom);
            }

            // 绘制右边框（只有第一列和最后一列）
            if (columnIndex == 0 || columnIndex == 4)
            {
                graphics.DrawLine(borderPen, cellRect.Right, cellRect.Top, cellRect.Right, cellRect.Bottom);
            }
        }

        /// <summary>
        /// 获取单元格背景颜色
        /// </summary>
        private static Color GetCellBackgroundColor(int columnIndex) =>
            columnIndex == 1 ? SkyBlueBackgroundColor : Color.White;

        /// <summary>
        /// 绘制完美文字效果
        /// </summary>
        private static void DrawPerfectText(Graphics graphics, string text, Font font, SolidBrush brush, Rectangle rect, bool addShadow)
        {
            StringFormat stringFormat = new StringFormat
            {
                Alignment = StringAlignment.Center,
                LineAlignment = StringAlignment.Center,
                FormatFlags = StringFormatFlags.NoWrap
            };

            // 为彩色背景添加文字阴影以增强可读性
            if (addShadow && brush.Color == Color.White)
            {
                using SolidBrush shadowBrush = new SolidBrush(Color.FromArgb(100, 0, 0, 0));
                Rectangle shadowRect = new Rectangle(rect.X + 1, rect.Y + 1, rect.Width, rect.Height);
                graphics.DrawString(text, font, shadowBrush, shadowRect, stringFormat);
            }

            graphics.DrawString(text, font, brush, rect, stringFormat);
        }

        /// <summary>
        /// 绘制带轮廓的文字效果 - 番摊列专用
        /// </summary>
        private static void DrawOutlinedText(Graphics graphics, string text, Font font, SolidBrush brush, Rectangle rect)
        {
            StringFormat stringFormat = new StringFormat
            {
                Alignment = StringAlignment.Center,
                LineAlignment = StringAlignment.Center,
                FormatFlags = StringFormatFlags.NoWrap
            };

            // 绘制黑色轮廓 - 在8个方向绘制黑色文字
            using (SolidBrush outlineBrush = new SolidBrush(Color.Black))
            {
                int outlineWidth = 1;
                for (int x = -outlineWidth; x <= outlineWidth; x++)
                {
                    for (int y = -outlineWidth; y <= outlineWidth; y++)
                    {
                        if (x != 0 || y != 0) // 跳过中心点
                        {
                            Rectangle outlineRect = new Rectangle(rect.X + x, rect.Y + y, rect.Width, rect.Height);
                            graphics.DrawString(text, font, outlineBrush, outlineRect, stringFormat);
                        }
                    }
                }
            }

            // 绘制白色主文字
            graphics.DrawString(text, font, brush, rect, stringFormat);
        }

        /// <summary>
        /// 绘制号码背景 - 放大背景覆盖区域
        /// </summary>
        private static void DrawNumberBackground(Graphics graphics, string text, Font font, Rectangle cellRect, Color backgroundColor)
        {
            // 测量文字尺寸
            SizeF textSize = graphics.MeasureString(text, font);

            // 计算背景矩形 - 调小背景面积
            int padding = 6; // 从8调小到6，减小背景面积
            int bgWidth = (int)textSize.Width + padding * 2;
            int bgHeight = (int)textSize.Height + padding + 2; // 减小额外高度

            // 居中定位背景矩形
            int bgX = cellRect.X + (cellRect.Width - bgWidth) / 2;
            int bgY = cellRect.Y + (cellRect.Height - bgHeight) / 2;

            Rectangle backgroundRect = new Rectangle(bgX, bgY, bgWidth, bgHeight);

            // 绘制圆角背景
            using SolidBrush backgroundBrush = new SolidBrush(backgroundColor);
            FillRoundedRectangle(graphics, backgroundBrush, backgroundRect, 3); // 添加圆角效果
        }

        /// <summary>
        /// 绘制彩色圆角边框 - 番摊列专用
        /// </summary>
        private static void DrawColoredNumberBorder(Graphics graphics, string text, Font font, Rectangle cellRect)
        {
            // 测量文字尺寸
            SizeF textSize = graphics.MeasureString(text, font);

            // 计算边框矩形 - 调小边框以适应14px字体
            int padding = 4; // 从5进一步减少到4，适应更大字体
            int borderWidth = (int)textSize.Width + padding * 2;
            int borderHeight = (int)textSize.Height + padding - 3; // 进一步减小高度

            // 居中定位边框矩形
            int borderX = cellRect.X + (cellRect.Width - borderWidth) / 2;
            int borderY = cellRect.Y + (cellRect.Height - borderHeight) / 2;

            Rectangle borderRect = new Rectangle(borderX, borderY, borderWidth, borderHeight);

            // 根据数字获取填充颜色
            Color fillColor = GetFanTanColor(text);

            // 取消绘制圆角填充背景，进一步排查边框问题
            // using (SolidBrush fillBrush = new SolidBrush(fillColor))
            // {
            //     FillRoundedRectangle(graphics, fillBrush, borderRect, 4);
            // }

            // 取消绘制圆角边框，避免与表格边框冲突
            // using (Pen borderPen = new Pen(Color.Black, 0.5f))
            // {
            //     DrawRoundedRectangle(graphics, borderPen, borderRect, 4);
            // }
        }

        /// <summary>
        /// 根据番摊数字获取填充颜色
        /// </summary>
        private static Color GetFanTanColor(string number)
        {
            return int.TryParse(number, out int index) && index is >= 1 and <= 4
                ? FanTanColors[index]
                : Color.White;
        }

        /// <summary>
        /// 创建圆角矩形路径
        /// </summary>
        private static GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();
            // 添加圆角矩形路径
            path.AddArc(rect.X, rect.Y, radius * 2, radius * 2, 180, 90); // 左上角
            path.AddArc(rect.Right - radius * 2, rect.Y, radius * 2, radius * 2, 270, 90); // 右上角
            path.AddArc(rect.Right - radius * 2, rect.Bottom - radius * 2, radius * 2, radius * 2, 0, 90); // 右下角
            path.AddArc(rect.X, rect.Bottom - radius * 2, radius * 2, radius * 2, 90, 90); // 左下角
            path.CloseFigure();
            return path;
        }

        /// <summary>
        /// 填充圆角矩形
        /// </summary>
        private static void FillRoundedRectangle(Graphics graphics, SolidBrush brush, Rectangle rect, int radius)
        {
            using GraphicsPath path = CreateRoundedRectanglePath(rect, radius);
            graphics.FillPath(brush, path);
        }

        /// <summary>
        /// 绘制圆角矩形
        /// </summary>
        private static void DrawRoundedRectangle(Graphics graphics, Pen pen, Rectangle rect, int radius)
        {
            using GraphicsPath path = CreateRoundedRectanglePath(rect, radius);
            graphics.DrawPath(pen, path);
        }

        #endregion
    }
}