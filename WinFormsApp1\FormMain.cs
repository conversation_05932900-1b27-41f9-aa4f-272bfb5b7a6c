namespace WinFormsApp1;

public partial class FormMain : Form
{
    public FormMain()
    {
        InitializeComponent();
    }

    private void FormMain_Load(object sender, EventArgs e)
    {
        // 原有单个数据集测试
        List<LotteryResult> lotteryResults =
        [
            new("20250902001", 11),
            new("20250902002", 12),
            new("20250902003", 13),
            new("20250902004", 14),
            new("20250902005", 11),
            new("20250902006", 12),
            new("20250902007", 13),
            new("20250902008", 14)
        ];

        // 生成传统表格
        Bitmap img = EnhancedLotteryTableDrawer.DrawPerfectLotteryTable(lotteryResults);
        img.Save(@"LotteryTable_Single.png");

        // 生成单个番摊表格
        var img2 = EnhancedLotteryTableDrawer.DrawTanImage(lotteryResults, @"宾果3");
        img2.Save(@"TanImage_Single.png");

        // 测试多数据集功能
        TestMultipleTanImages();
        TestMultiplePerfectTables();
    }

    /// <summary>
    /// 测试多数据集番摊表格绘制功能
    /// </summary>
    private void TestMultipleTanImages()
    {
        // 创建多个游戏的数据集
        var lotteryResultsList = new List<List<LotteryResult>>
        {
            // 第1个游戏：宾果1
            new()
            {
                new(@"20250902001", 1),
                new(@"20250902002", 2),
                new(@"20250902003", 3),
                new(@"20250902004", 4),
                new(@"20250902005", 1),
                new(@"20250902006", 1),
                new(@"20250902007", 2),
                new(@"20250902008", 3),
                new(@"20250902009", 4),
                new(@"20250902010", 1)
            },

            // 第2个游戏：宾果2
            new()
            {
                new(@"20250902101", 13),
                new(@"20250902102", 24),
                new(@"20250902103", 31),
                new(@"20250902104", 42),
                new(@"20250902105", 53),
                new(@"20250902106", 64),
                new(@"20250902107", 71),
                new(@"20250902108", 83),
                new(@"20250902109", 94),
                new(@"20250902110", 81),
                new(@"20250902111", 72),
                new(@"20250902112", 63),
                new(@"20250902113", 54),
                new(@"20250902114", 41)
            },

            // 第3个游戏：宾果3
            new()
            {
                new(@"20250902201", 2),
                new(@"20250902202", 4),
                new(@"20250902203", 1),
                new(@"20250902204", 3),
                new(@"20250902205", 2),
                new(@"20250902206", 4),
                new(@"20250902207", 1),
                new(@"20250902208", 3),
                new(@"20250902209", 3)
            }
        };

        // 对应的游戏名称
        var gameNames = new List<string>
        {
            @"宾果1",
            @"宾果2",
            @"宾果3"
        };

        // 生成多数据集番摊表格
        var multiImg = EnhancedLotteryTableDrawer.DrawMultipleTanImages(lotteryResultsList, gameNames);
        multiImg.Save(@"TanImage_Multiple.png");
    }

    /// <summary>
    /// 测试多数据集传统表格绘制功能
    /// </summary>
    private void TestMultiplePerfectTables()
    {
        // 创建多个数据集用于传统表格测试
        var lotteryResultsList = new List<List<LotteryResult>>
        {
            // 第1个数据集：较少数据
            new List<LotteryResult>
            {
                new(@"20250902001", 1),
                new(@"20250902002", 2),
                new(@"20250902003", 3)
            },

            // 第2个数据集：中等数据
            new List<LotteryResult>
            {
                new(@"20250902101", 4),
                new(@"20250902102", 1),
                new(@"20250902103", 2),
                new(@"20250902104", 3),
                new(@"20250902105", 4)
            },

            // 第3个数据集：较多数据
            new List<LotteryResult>
            {
                new(@"20250902201", 2),
                new(@"20250902202", 3),
                new(@"20250902203", 4),
                new(@"20250902204", 1),
                new(@"20250902205", 2),
                new(@"20250902206", 3),
                new(@"20250902207", 4),
                new(@"20250902208", 1)
            }
        };

        // 生成多数据集传统表格
        var multiPerfectImg = EnhancedLotteryTableDrawer.DrawMultiplePerfectLotteryTables(lotteryResultsList);
        multiPerfectImg.Save(@"LotteryTable_Multiple.png");
    }
}